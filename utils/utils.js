/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-10-31 14:36:23
 * @Description: 工具函数
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-06 11:32:58
 * @FilePath: \jindian\utils\utils.js
 * @IDE: Created by VScode.
 */
import { addAccessLog, lzLogin } from './constants/api';
import { httpRequest } from './constants/http';
const store = require('../store/store');
const app = getApp();
const { checkShopMemberErrorCode, requestSubscribeMessageErrorCode, applyEcCouponErrorCode, getShopMemberInfoErrorCode, openWebcastRoomErrorCode, getShopRecordTokenErrorCode, updateShopMemberPointsErrorCode, getShopMemberReferralResultErrorCode, getUserProfileErrorCode, showDouyinOpenAuthErrorCode, openEcShopErrorCode, openEcGoodErrorCode, registerExclusivePriceActivityErrorCode } = app.require('/utils/constants/ttAPIErrorCode');
const { baseUrl } = app.require('/utils/constants/api');
let plugin;
if (tt.canIUse('requirePlugin')) {
  plugin = tt.requirePlugin('tt95aee3130ae1cbe911');
  console.log('插件初始化成功');
} else {
  console.log('插件初始化失败');
  plugin = tt;
}

/**防抖函数使用，用于记录延时函数 */
let timer;

const { activityId, shopId } = store.data;

/**埋点 */
export async function reportAccess(behavior) {
  try {
    const res = await addAccessLog({
      shopId: store.data.shopId,
      behavior,
    });
    console.log('埋点成功', res);
  } catch (error) {}
}

/**防抖函数 */
export function debounceImmediately(that, func, wait = 500) {
  return function () {
    if (timer) clearTimeout(timer);
    const callNow = !timer;
    timer = setTimeout(() => {
      timer = null;
    }, wait);
    if (callNow) func.apply(that, arguments);
  };
}

/**获取用户授权信息 */
export function authorize() {
  return new Promise((resolve) => {
    tt.authorize({
      scope: 'scope.userInfo',
      success(data) {
        // 用户同意授权用户信息
        console.log('success', data);
        resolve({
          success: true,
          data,
        });
      },
      fail(data) {
        console.log('fail', data);
        showSystemError();
      },
    });
  });
}
/**获取用户已授权信息 */
export function getSetting() {
  return new Promise((resolve) => {
    tt.getSetting({
      success(data) {
        resolve({
          success: true,
          data,
        });
      },
      fail(data) {
        console.log('fail', data);
        showSystemError();
        // resolve({ success: false, data });
      },
    });
  });
}

/**陆泽登陆 */
export function login() {
  return new Promise(async (resolve, reject) => {
    const {
      success,
      data: { code, anonymousCode },
    } = await ttLogin();

    if (!success) {
      store.setToken('登录失败');
      resolve(false);
    } else {
      try {
        const { activityId, shopId } = store.data;
        const res = await lzLogin({
          activityId,
          shopId,
          code,
          anonymousCode,
        });
        store.setToken(res.data.token);
        store.setOpenId(res.data.openId);
        resolve(true);
      } catch (error) {
        console.error(error);
        reject(false);
      }
    }
  });
}

/**用户登录 */
export function ttLogin() {
  return new Promise((resolve) => {
    tt.login({
      success: (data) => {
        console.log('登录', data);
        resolve({
          success: true,
          data,
        });
      },
      fail: (error) => {
        console.log('拒绝登录');
        resolve({
          success: false,
          error,
        });
      },
    });
  });
}

/** 检查用户当前登录状态是否过期 */
export function checkSession() {
  return new Promise((resolve) => {
    tt.checkSession({
      success() {
        resolve({
          success: true,
        });
      },
      fail() {
        resolve({
          success: false,
        });
      },
    });
  });
}

/**获取用户的基本信息，只可在 tap 事件回调中调用，每次调用都会弹出授权提示窗，若用户同意，则会返回用户的真实数据 */
export function getUserProfile() {
  return new Promise((resolve, reject) => {
    tt.getUserProfile({
      force: true, //当宿主未登录时，是否强制拉起登录框
      success(data) {
        resolve(data);
      },
      fail(error) {
        const { errNo } = error;
        console.warn('获取用户的基本信息【错误】==>', error, getUserProfileErrorCode[errNo]);
        reject(error);
      },
    });
  });
}

/**获取用户信息*/
export function getUserInfo() {
  return new Promise((resolve, reject) => {
    tt.getUserInfo({
      success(data) {
        resolve(data);
      },
      fail(error) {
        reject(error);
      },
    });
  });
}

/**获取用户设备信息 */
export function getSystemInfo() {
  return new Promise((resolve, reject) => {
    const menuButtonObject = tt.getMenuButtonBoundingClientRect();
    tt.getSystemInfo({
      success: (res) => {
        const statusBarHeight = res.statusBarHeight,
          navTop = menuButtonObject.top, //胶囊按钮与顶部的距离
          navHeight = statusBarHeight + menuButtonObject.height + (menuButtonObject.top - statusBarHeight) * 2, //导航高度
          windowHeight = res.windowHeight;

        resolve({
          status: true,
          data: {
            navHeight,
            navTop,
            statusBarHeight,
            windowHeight,
          },
        });
      },
      fail(err) {
        resolve({
          status: false,
          data: err,
        });
      },
    });
  });
}

/**
 * 加密用户姓名
 * @param {String} username
 * @returns String
 */
export function encryptUsername(username) {
  const { length } = username;

  if (!length) return '';

  switch (length) {
    case 1:
      return username;
    case 2:
      return username[0] + '*';
    case 3:
      return username[0] + '*' + username[length - 1];

    default:
      return username[0] + '**' + username[length - 1];
  }
}

/**
 * 为数字加千位分隔符 - 过滤器 2.0版本
 * @param {String | Number} num
 * @returns String
 */
export function SetKiloCharacter(num) {
  if (!num) return '0';

  if (typeof num === 'number') {
    return numSetKiloCharacter(num);
  } else if (typeof num === 'string') {
    const res = num.split(',').length - 1 ? num : numSetKiloCharacter(parseFloat(num));
    return res;
  } else {
    return num;
  }

  /**number 转string并添加千分位, */
  function numSetKiloCharacter(num) {
    const res = num.toString().replace(/\d+/, function (n) {
      // 先提取整数部分
      return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
        return $1 + ',';
      });
    });
    return res;
  }
}
// 获取设备信息
export function getSystemInfoSync() {
  const { platform, appName } = tt.getSystemInfoSync(true);
  console.log('【设备信息】', {
    platform,
    appName,
  });
  return {
    platform,
    appName,
  };
}

/**检测当前用户的抖音版本是否支持该接口能力 */
export function isVersioningSupport() {
  const isVersioningSupport = tt.canIUse('getShopMemberReferralResult');

  if (!isVersioningSupport) return isVersioningSupportLower(10000);
  return isVersioningSupport;
}

/**抖音版本过低的提示 */
export function isVersioningSupportLower(time = 1500) {
  showToast(
    '系统检测到您的抖音版本过低，请升级后使用',
    'none',
    time,
    true, //遮罩不生效
  );
}

/**
 * 检查当前用户是否为会员
 * @param {*} shopId
 * @returns
 */
export function checkShopMember(shopId) {
  return new Promise((resolve, reject) => {
    console.log('检查当前用户是否为会员', shopId);
    plugin.checkShopMember({
      shopId: shopId,
      success(data) {
        console.log('检查当前用户是否为会员【成功】==>', data);
        resolve({
          success: true,
          isMember: data.isMember,
          data,
        });
      },
      fail(error) {
        console.log(error);
        const { errNo } = error;
        console.warn('检查当前用户是否为会员【错误】==>', checkShopMemberErrorCode[errNo]);
        showToast(checkShopMemberErrorCode[errNo]);
        reject({ success: false, error });
      },
    });
  });
}

/** 查询用户会员身份信息 */
export function getShopMemberInfo() {
  return new Promise((resolve) => {
    plugin.getShopMemberInfo({
      shopId: shopId,
      success(data) {
        console.log('获取会员信息【成功】==>', data);
        resolve({
          success: true,
          memberCardInfo: data.memberCardInfo,
        });
      },
      fail(error) {
        const { errNo } = error;
        console.warn('获取会员信息【错误】==>', getShopMemberInfoErrorCode[errNo]);
        showToast(getShopMemberInfoErrorCode[errNo]);
        // resolve({ success: false, error });
      },
    });
  });
}

/** 查询会员裂变结果 */
export function getShopMemberReferralResult({ shopId = '', activityId = '', pageNo = 1, pageSize = 20 }) {
  return new Promise((resolve) => {
    plugin.getShopMemberReferralResult({
      shopId,
      activityId,
      pagination: {
        pageNo,
        pageSize,
      },
      success(data) {
        console.log('查询会员裂变结果【成功】==>', data);
        resolve({
          success: true,
          referralCount: data.referralCount,
          referralList: data.referralList,
        });
      },
      fail(error) {
        const { errNo } = error;
        console.warn('查询会员裂变结果【错误】==>', getShopMemberReferralResultErrorCode[errNo], {
          shopId,
          activityId,
          pageNo,
          pageSize,
        });
        // showSystemError();

        //服务器内部错误，则再1秒后，再次请求
        if (errNo === 13000) {
          setTimeout(() => {
            getShopMemberReferralResult({
              shopId,
              activityId,
              pageNo,
              pageSize,
            });
          }, 1000);
        }
      },
    });
  });
}

/**用户订阅信息 */
export function requestSubscribeMessage(tmplIds = []) {
  return new Promise((resolve) => {
    tt.requestSubscribeMessage({
      tmplIds, // 需要填入开放平台申请的模版id，支持最多3个同类型模版
      success(data) {
        showToast('订阅成功', 'success');
        resolve({
          success: true,
          data,
        });
      },
      fail(error) {
        const { errNo } = error;
        //订阅失败
        showToast(requestSubscribeMessageErrorCode[errNo], 'fail');
        console.warn('用户订阅信息【错误】==>', requestSubscribeMessageErrorCode[errNo]);
        resolve({
          success: false,
          error,
        });
      },
      complete(res) {
        //订阅完成
        console.log('tt.requestSubscribeMessage API调用完成: ', res);
      },
    });
  });
}

//唤起抖音收货地址
export function openChooseAddress() {
  return new Promise((resolve) => {
    tt.chooseAddress({
      success(res) {
        console.log('地址选择器返回结果', res);
        const params = {
          receiver: res.userName,
          province: res.provinceName,
          city: res.cityName,
          region: res.countyName,
          addressDetail: res.detailInfo,
          phoneNumber: res.telNumber,
        };
        resolve(params);
      },
      async fail(err) {
        console.log('fail', err);
        if (err.errNo) {
          if (err.errNo == 10200) {
            //用户拒绝授权
            showToast('请在权限设置里打开地址授权');
            await openSetting();
          }
          if (err.errNo == 10502) {
            //取消选择
            resolve(false);
          }
        }
        resolve(false);
      },
    });
  });
}

export function toLink(url) {
  let tabList = ['/pages/index/index', '/pages/test-tab/test-tab'];
  let status = false;
  if (url.indexOf('https') != -1) {
    url = `/pages/web-view/web-view?url=${encodeURIComponent(url)}`;
  }
  tabList.forEach((item) => {
    if (url.indexOf(item) != -1) {
      console.log(item, `${item}`, url);
      status = true;
      tt.switchTab({
        url: `${item}`,
        success: (res) => {
          console.log(res, 'success');
        },
        fail: (res) => {
          console.log(res, 'fail');
        },
      });
      return;
    }
  });
  if (status) return;
  tt.navigateTo({
    url,
    success: (res) => {
      console.log(res, 'success');
    },
    fail: (res) => {
      console.log(res, 'fail');
    },
  });
}
/** 校验数据合法性 */
export function verifyData(params) {
  const { receiver, addressDetail, phoneNumber, province, city, region, postCode } = params;
  console.log('verifyData', params);

  if (!receiver) {
    showToast('未填写收货人姓名');
    return false;
  }
  if (iosFace.test(receiver) || androidFace.test(receiver)) {
    showToast('昵称不能含有表情');
    return false;
  }

  if (!phoneNumber) {
    showToast('未填写手机号');
    return false;
  }
  if (phoneNumber.length < 11) {
    showToast('请填写11位手机号');
    return false;
  }

  if (!isMobile(phoneNumber)) {
    showToast('您的手机号输入错误');
    return false;
  }

  if (!province || !city || !region) {
    showToast('未选择地区');
    return false;
  }

  if (!addressDetail) {
    showToast('未填写详细地址');
    return false;
  }

  if (iosFace.test(addressDetail) || androidFace.test(addressDetail)) {
    showToast('详细地址不能含有表情');
    return false;
  }
  if (!postCode) {
    showToast('未填写邮编');
    return false;
  }

  if (postCode.length < 6) {
    showToast('请填写6位邮编');
    return false;
  }

  return true;
}

/**判断手机号是否合法 */
export function isMobile(value) {
  value = value.replace(/[^-|\d]/g, '');
  return /^1[3-9]\d{9}$/.test(value);
  return /^((\+86)|(86))?(1)\d{10}$/.test(value) || /^0[0-9-]{10,13}$/.test(value);
}

/**打开设置页面，返回用户设置过的授权结果。 */
export function openSetting() {
  return new Promise((resolve) => {
    tt.openSetting({
      success(res) {
        console.log('打开设置页', res.authSetting['scope.userInfo']);
        resolve(res.authSetting['scope.userInfo']);
      },
      fail(error) {
        console.warn('打开设置页面，返回用户设置过的授权结果。【错误】==>', error);
        resolve(false);
      },
    });
  });
}

/**显示当前小程序页面的转发按钮。转发按钮位于小程序页面右上角的“更多”中。 */
export function showShareMenu() {
  return new Promise((resolve) => {
    tt.showShareMenu({
      menus: ['share', 'record'],
      success(data) {
        console.log('已成功显示转发按钮', data);
        resolve({
          success: true,
          data,
        });
      },
      fail(error) {
        console.log('showShareMenu 调用失败', error.errMsg);
        resolve({
          success: false,
          error,
        });
      },
      complete(res) {},
    });
  });
}

/**用户领取优惠券,使用该接口时需要用户身份信息，请确保在调用接口前，用户已经登陆宿主 APP */
export function applyEcCoupon(shopId = '', couponMetaId = '', couponCount = 1) {
  return new Promise((resolve, reject) => {
    plugin.applyEcCoupon({
      shopId,
      couponMetaId: couponMetaId,
      couponCount: 1,
      success(data) {
        console.log('券码列表', data.couponIdList); // 可以使用couponIdList做后续逻辑
        resolve(data);
      },
      fail(error) {
        console.error(error);
        const { errNo } = error;
        console.warn('用户领取优惠券【错误】==>', applyEcCouponErrorCode[errNo]);
        showToast(applyEcCouponErrorCode[errNo], 'none', 5000);
        reject(applyEcCouponErrorCode[errNo]);
      },
    });
  });
}
/**给用户发放积分 */
export function updatePoint(type = 1, amount = 1) {
  return new Promise((resolve) => {
    // 1.获取recordToken
    plugin.getShopRecordToken({
      shopId: shopId,
      type: 1, //"记录标识。1: 更新积分 （当前仅支持更新积分）"
      success: ({ recordToken }) => {
        // 2. 更新会员积分
        plugin.updateShopMemberPoints({
          shopId: shopId,
          updateInfo: {
            type, //"积分变动类型：1=增加，2=减少"
            amount, //增加/减少的积分值，大于 等于1 的整数
            recordToken, // 通过tt.getShopRecordToken()获取recordToken
          },
          success: (res) =>
            resolve({
              success: true,
              res,
            }),
          fail: (error) => {
            const { errNo } = error;
            console.warn('用户领取积分【错误】==>', updateShopMemberPointsErrorCode[errNo]);
            showToast(updateShopMemberPointsErrorCode[errNo], 'none', 5000);
            resolve({
              success: false,
              error,
            });
          },
        });
      },
      fail: (error) => {
        const { errNo } = error;
        console.warn('用户领取积分获取recordToken【错误】==>', getShopRecordTokenErrorCode[errNo]);
        showToast(getShopRecordTokenErrorCode[errNo], 'none', 5000);
        resolve({
          success: false,
          error,
        });
      },
    });
  });
}

/**跳转至直播间 */
export function openWebcastRoom(awemeId = '') {
  return new Promise((resolve) => {
    tt.openWebcastRoom({
      awemeId,
      success(res) {
        resolve({
          success: true,
        });
      },
      fail(error) {
        const { errNo } = error;
        console.warn('跳转至直播间【错误】==>', openWebcastRoomErrorCode[errNo]);
        showToast(openWebcastRoomErrorCode[errNo], 'none', 5000);
      },
    });
  });
}

/**用户授权信息，获取用户直播间点赞信息，抖音视频点赞信息 */
export function showDouyinOpenAuth() {
  return new Promise((resolve) => {
    tt.showDouyinOpenAuth({
      scopes: {
        'video.behavior': 1,
        // "live.behavior": 1,
      },
      success(data) {
        console.log('用户授权成功', data);

        resolve({
          success: true,
          ticket: data.ticket,
        });
      },
      fail(error) {
        const { errNo } = error;
        console.warn('用户授权信息【错误】==>', showDouyinOpenAuthErrorCode[errNo]);
        showToast(showDouyinOpenAuthErrorCode[errNo], 'none', 5000);
      },
      complete(res) {},
    });
  });
}

/**跳转短视频 */
export function openSchema(schema = '') {
  return new Promise((resolve) => {
    tt.openSchema({
      schema: 'snssdk1128://aweme/detail/' + schema + '?refer=mp_page',
      external: true,
      success(res) {
        resolve({
          success: true,
        });
      },
      fail(error) {
        console.warn('跳转短视频【错误】==>', error);
      },
    });
  });
}

/**跳转至商家店铺 */
export function openEcShop({ shopId, tabType = 1 }) {
  return new Promise((resolve) => {
    plugin.openEcShop({
      shopId: shopId,
      tabType, // 1精选 2分类 3商品 4大促 5旧ISV 6会员 7新ISV
      success: (res) => {
        resolve({
          success: true,
        });
      },
      fail: (error) => {
        const { errNo } = error;
        console.warn('跳转至商家店铺【错误】==>', openEcShopErrorCode[errNo]);
        showToast(openEcShopErrorCode[errNo], 'none', 5000);
      },
    });
  });
}

/**跳转至商品详情 */
export function openEcGood(promotionId) {
  return new Promise((resolve) => {
    plugin.openEcGood({
      promotionId,
      success: (res) => {
        console.log('跳转至商品详情', res);
        resolve({
          success: true,
        });
      },
      fail: (error) => {
        console.error(error);
        const { errNo } = error;
        console.warn('跳转至商品详情【错误】==>', openEcGoodErrorCode[errNo]);
        showToast(openEcGoodErrorCode[errNo], 'none', 5000);
      },
    });
  });
}

/**专享价活动人群注册 */
export function registerExclusivePriceActivity(goodsId, activityId, buyLimit = 1) {
  return new Promise((resolve) => {
    // requestOrder
    tt.registerExclusivePriceActivity({
      shopId: shopId,
      activityId,
      buyLimit,
      success: (res) => {
        resolve({
          success: true,
        });
        openEcGood(goodsId);
      },
      fail: (error) => {
        const { errNo } = error;
        console.warn('专享价活动人群注册【错误】==>', error, registerExclusivePriceActivityErrorCode[errNo]);
        showToast(registerExclusivePriceActivityErrorCode[errNo], 'none', 5000);
      },
    });
  });
}

/**展示轻提醒 */
export function showToast(title = '', icon = 'none', duration = 2000, mask = false) {
  tt.showToast({
    title,
    icon,
    duration,
    mask,
  });
}

/**显示错误信息 */
export function showSystemError() {
  showToast('系统繁忙！请稍后再试~', 'none', 10000);
}

/**显示错误信息 */
export function showError() {
  showToast('哎呀，活动太火爆了！请稍后再试~', 'none', 10000);
}

// iOS和安卓表情代码
export const iosFace = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
export const androidFace = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;

/**获取随机颜色值 */
export function getRandomColor() {
  function getColor(color) {
    return (color += '0123456789abcdef'[Math.floor(Math.random() * 16)]) && color.length == 6 ? color : getColor(color);
  }

  return '#' + getColor('');
}

/**将秒转为分钟 */
export function formatSeconds(value) {
  if (!value) return 0;
  var secondTime = parseInt(value); // 秒
  var minuteTime = 0; // 分
  var hourTime = 0; // 小时
  if (secondTime > 60) {
    //如果秒数大于60，将秒数转换成整数
    //获取分钟，除以60取整数，得到整数分钟
    minuteTime = parseInt(secondTime / 60);
    //获取秒数，秒数取佘，得到整数秒数
    secondTime = parseInt(secondTime % 60);
    //如果分钟大于60，将分钟转换成小时
    if (minuteTime > 60) {
      //获取小时，获取分钟除以60，得到整数小时
      hourTime = parseInt(minuteTime / 60);
      //获取小时后取佘的分，获取分钟除以60取佘的分
      minuteTime = parseInt(minuteTime % 60);
    }
  }
  var result = '' + parseInt(secondTime) + '秒';

  if (minuteTime > 0) {
    result = '' + parseInt(minuteTime) + '分' + result;
  }
  if (hourTime > 0) {
    result = '' + parseInt(hourTime) + '小时' + result;
  }
  return result;
}

/**
 * 时间格式化函数
 * @param {number} timestamp 时间戳/ -- 若传入出参格式，则timestamp默认取当前时间
 * @param {string} formats   出参形式
 * @description formats格式包括
 * @example
 *  1. Y-m-d
 *  2. Y-m-d H:i:s
 *  3. Y年m月d日
 *  4. Y年m月d日 H时i分
 *  5. m-d H:i
 */
export function dateFormat(timestamp = new Date().getTime(), formats = 'Y-m-d H:i') {
  if (!timestamp) timestamp = new Date().getTime(); //拦截 用户timestamp设置的入参为 null/undefined /NaN时的情况

  if (`${timestamp}`.length === 10) timestamp *= 1000; //如果得到是秒时间戳则转为 毫秒时间戳

  const inputDate = new Date(timestamp * 1);

  /**入参小于10的数字为，为其前方补0 */

  const padStart = (num) => `${num}`.padStart(2, '00');

  const year = inputDate.getFullYear();
  const month = padStart(inputDate.getMonth() + 1);
  const day = padStart(inputDate.getDate());

  const hour = padStart(inputDate.getHours());
  const minute = padStart(inputDate.getMinutes());
  const second = padStart(inputDate.getSeconds());

  return formats.replace(/Y|m|d|H|i|s/gi, function (matches) {
    return {
      Y: year,
      m: month,
      d: day,
      H: hour,
      i: minute,
      s: second,
    }[matches];
  });
}

/**跳转回主会场 */
export function redirectToHome() {
  tt.redirectTo({
    url: `/pages/index/index`,
  });
}


/**
 * 埋点方法
 * @param {Object} options 埋点参数
 * @param {string} options.c 具体内容标识
 * @param {string} options.e 事件类型: 'enter' 或 'click'
 */
export function reportBuryPoint(options = {}) {
  try {
    const { c = '', e = 'enter' } = options;

    const params = {
      prd: 'crm',
      te: 'c',
      sid: store.data.shopId || '',
      uid: store.data.openId || '',
      t: new Date().getTime(),
      opid: store.data.activityId || '',
      ver: '1',
      c: c,
      e: e,
      l: '1',
      vid: '1',
      url: getCurrentPages().length > 0 ? getCurrentPages()[getCurrentPages().length - 1].route : '',
      ch: '',
      at: '99'
    };
    
    console.log('埋点参数', params);

    tt.request({
      operation: '前端埋点',
      url: 'https://jdsupport-api.dianpusoft.cn/burypoint/getBuryPointBehaviors',
      method: 'POST',
      params
    });
  } catch (error) {
    console.error('埋点失败:', error);
  }
}

/**
 * PV/UV 埋点 (进入)
 * @param {string} pageId 页面标识
 */
export function reportPageView(val) {
  reportBuryPoint({
    c: val,
    e: 'enter'
  });
}

/**
 * 点击埋点
 * @param {string} elementId 元素标识
 */
export function reportClick(val) {
  reportBuryPoint({
    c: val,
    e: 'click'
  });
}

/**
 * 开卡埋点
 */
export function reportOpenCard() {
  reportBuryPoint({
    c: 'openCard',
    e: 'click'
  });
}