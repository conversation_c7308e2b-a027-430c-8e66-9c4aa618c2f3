import create from 'mini-stores';
import { addNewMember } from '../../utils/constants/api';
import { reportOpenCard } from '../../utils/utils';

const app = getApp();
const store = require('../../store/store');
const stores = {
  $store: store, // axml视图上使用$store.xxx即对应store.data.xxx的值。
};
create.Component(stores, {
  data: {
    name: 'lz-member',
    bgImg: '',
  },
  properties: {},

  created() {},
  methods: {
    onClose() {
      this.triggerEvent('close');
    },
    handleJoinMemberError(e) {
      console.log('erro', e);
    },
    async handleJoinMemberSuccess(e) {
      console.log('success', e);
      store.data.isMember = true;
      store.update();
      try {
        const res = await addNewMember({
          openCardStatus: 1,
        });
        reportOpenCard();
      } catch (error) {
        console.error(error);
      }
      this.triggerEvent('close');
    },
    handleJoinMemberCancel(e) {
      console.log('cancel', e);
    },
  },
});
