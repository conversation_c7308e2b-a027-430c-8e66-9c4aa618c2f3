import create from 'mini-stores';
import store from '../../store/store';
import { getSystemInfoSync, isVersioningSupport, checkShopMember, login, showToast, getUserProfile, reportAccess, reportPageView, reportClick, openEcGood } from '../../utils/utils.js'; //工具函数集合
import {
  syncUser,
  getActivityInfo,
  getUserQualification,
  addNewMember,
  getActivitId,
  getSkuPages,
  applyPrize,
  receivePrize,
} from '../../utils/constants/api';
import dayjs from 'dayjs';
import { httpRequest } from '../../utils/constants/http';
const app = getApp();

const stores = {
  $store: store, // axml视图上使用$store.xxx即对应store.data.xxx的值。
};
create.Page(stores, {
  data: {
    loading: false,
    name: 'lz-main',
    memberPop: false,
    recordPop: false,
    rulePop: false,
    addressPop: false,
    successApplyPop: false,
    successReceivePop: false,
    sorryPop: false,
  },
  async onShareAppMessage(option) {
    return {
      path: `/pages/index/index?shopId=${store.shopId}&activityId=${st}`, // ?后面的参数会在转发页面打开时传入onLoad方法
      success() {
        console.log('转发发布器已调起，并不意味着用户转发成功，微头条不提供这个时机的回调');
      },
      fail() {
        console.log('转发发布器调起失败');
      },
    };
  },
  onLoad: async function (option) {
    console.log('通过分享打开，才有此参数', option);
    console.log(store.applyBtnStatus);

    if (option.shopId && option.activityId) {
      store.data.shopId = option.shopId;
      store.data.activityId = option.activityId;
      store.update();
    } else {
      const sceneId = tt.getStorageSync('scene');
      console.log('通过分享打开，才有此参数', sceneId);
      const res = await getActivitId({
        shopId: store.data.shopId,
        sceneId,
      });
      store.data.activityId = res.data;
      store.update();
    }
    this.init();
    // // TODO:测试数据
    // // 手动设置测试数据
    // store.setDecoData({
    //   // kv图
    //   pageBg: "https://img10.360buyimg.com/imgzone/jfs/t1/314277/25/14087/270763/68663e66F3f152fdf/a8010d378f3876ce.png",
    //   // 领取攻略图
    //   actStrategy: "https://img10.360buyimg.com/imgzone/jfs/t1/288964/33/7801/15710/686654aaF429daeef/95edab0aeb8b23e7.png",
    //   // 报名成功弹窗图
    //   signSuccessPopup: "https://img10.360buyimg.com/imgzone/jfs/t1/293268/2/17065/47733/686b30bcFf3cba3d0/5bbc4c36780657d0.png",
    //   // 报名失败弹窗图
    //   signFailPopup: "//img10.360buyimg.com/imgzone/jfs/t1/295898/36/15355/48474/686b90a1Feceb4587/ef464a48e7d93d38.png",
    //   // 领取失跳转类型
    //   drawFailPopup: "//img10.360buyimg.com/imgzone/jfs/t1/300428/13/20737/53131/686b90a2F5335905e/a42f0af1a231381b.png",
    //   signFailBtnType0: 1, // 符合条件订单=0   报名失败弹跳转类型   1-店铺首页 2-店铺自定义页 3-新客礼1.0   4-新客礼2.0   5-集罐礼
    //   signFailBtnId0: '',  // 报名失败弹跳转小程序id
    //   signFailBtnType1: 1, // 符合条件订单>1   报名失败弹跳转类型   1-店铺首页 2-店铺自定义页 3-新客礼1.0   4-新客礼2.0   5-集罐礼
    //   signFailBtnId1: '',  // 报名失败弹跳转小程序id
    //   drawFailBtnId: '', //领取失跳转id
    // });
  },
  async onShow() {},

  // 获取曝光商品
  async getSkuList () {
    try {
      const res = await getSkuPages({
        current: store.data.pageInfo.current,
        size: 10,
        type: 2,
      });
      store.data.skuList = res.data.records;
      store.data.pageInfo = {
        total:res.data.total,
        current:res.data.current,
        size:res.data.size,
        pages: res.data.pages,
      }
      store.update();
    } catch (error) {
      console.error(error);
    }
  },

  // 加载更多
  async loadMore () {
    store.data.pageInfo.current++;
    await this.getSkuList();
  },

  async init() {
    tt.showLoading({
      title: '加载中...',
      mask: true,
    });

    const systemInfo = getSystemInfoSync();
    const {
      platform, // android
      appName,
    } = systemInfo;
    store.data.platform = platform;
    store.update();

    //调用login
    await login();

    // 获取活动信息
    try {
      const res = await getActivityInfo();
      tt.setNavigationBarTitle({
        title: res.data.activityName,
      });
      store.setDecoData(JSON.parse(res.data.decoData));
      store.setRule(res.data.rule);
      store.setPrizeInfo(res.data.prizeInfo);
      store.setApplyBtnStatus(res.data.applyBtnStatus);
      this.getSkuList();
      if (dayjs().isBefore(res.data.startTime)) {
        store.setActStatus(1);
        showToast('活动未开始');
      } else if (dayjs().isAfter(res.data.endTime)) {
        store.setActStatus(3);
        showToast('活动已结束');
      } else {
        store.setActStatus(2);
      }
    } catch (error) {}

    //如果当前是IDE,则不校验抖音版本
    if (platform !== 'devtools') {
      console.log('666');
      //检测当前用户的抖音版本是否支持该接口能力
      if (!isVersioningSupport()) return store.setIsVersioningSupport(false);
      //如果是会员，获取会员信息
      const res = await checkShopMember(store.data.shopId);
      if (res.success) {
        store.data.isMember = res.isMember;
        store.update();
        if (!res.isMember) {
          this.setData({
            memberPop: true,
          });
        } else {
          try {
            const res = await addNewMember({
              openCardStatus: 2,
            });
          } catch (error) {
            console.error(error);
          }
        }
      }
    }
    this.setData({
      loading: true,
    });
    tt.hideLoading();
    reportAccess('enter');
    // 进入页面埋点
    reportPageView(store.data.doudianOpenId);
  },

  async asyncUserInfo() {
    try {
      const { rawData, signature } = await getUserProfile();
      await syncUser({
        rawData,
        signature,
      });
    } catch (error) {
      console.error(error);
    }
  },

  // 报名参与
  async applyAct() {
    // 发送报名按钮点击埋点
    reportClick('bmcy');

    if (store.data.actStatus === 1) {
      showToast('活动未开始');
      return;
    }
    if (store.data.actStatus === 3) {
      showToast('活动已结束');
      return;
    }
    // TODO 会员这好像有问题
    // 校验是否是会员
    if (!store.data.isMember) {
      this.setData({
        memberPop: true,
      });
      return;
    }
    // 检查是否报名
    if (store.data.applyBtnStatus === 2) {
      showToast('您已经报过名了');
      return;
    }
    // 检查是否到报名时间
    if (store.data.applyBtnStatus === 3) {
      showToast('不在报名时段');
      return;
    }
    // 报名参与-clickType 0
    store.data.clickType = 0;
    if (!store.data.hasGetUserInfo) {
      // 同步用户信息
      await this.asyncUserInfo();
    }
    tt.showLoading({
      title: '加载中...',
      mask: true,
    });
    try {
      const res = await applyPrize();
      if (res.data.success) {
        // 成功
        this.setData({
          successApplyPop: true,
        });
      } else {
        // 失败
        this.setData({
          sorryPop: true,
        });
      }
      this.init();
      tt.hideLoading();
    } catch (error) {
      tt.hideLoading();
      console.log(error.message);
      this.setData({
        sorryPop: true,
      });
      this.init();
    }
  },

  // 领取奖品
  async receivePrize() {
    // 发送领取奖品按钮点击埋点
    reportClick('ljjp');

    if (store.data.actStatus === 1) {
      showToast('活动未开始');
      return;
    }
    if (store.data.actStatus === 3) {
      showToast('活动已结束');
      return;
    }
    // TODO 会员这好像有问题
    // 校验是否是会员
    if (!store.data.isMember) {
      this.setData({
        memberPop: true,
      });
      return;
    }    
    // 领取奖品-clickType 1
    store.data.clickType = 1;
    if (!store.data.hasGetUserInfo) {
      // 同步用户信息
      await this.asyncUserInfo();
    }
    tt.showLoading({
      title: '加载中...',
      mask: true,
    });
    try {
      const res = await receivePrize();
      if (res.success) {
        // 成功
        this.setData({
          successReceivePop: true,
        });
      } else {
        // 失败
        this.setData({
          sorryPop: true,
        });
      }
      this.init();
      tt.hideLoading();
    } catch (error) {
      console.error(error);
      this.setData({
        sorryPop: true,
      });
      this.init();
    }
  },

  // 兑礼记录弹窗
  showRecord() {
    // 发送记录按钮点击埋点
    reportClick('兑礼记录');

    this.setData({
      recordPop: true,
    });
  },

  // 兑礼规则弹窗
  showRule() {
    // 兑礼规则按钮点击埋点
    reportClick('兑礼规则');

    this.setData({
      rulePop: true,
    });
  },

  // 打开填写地址弹窗
  openAddressPop() {
    this.setData({
      successReceivePop: false,
      addressPop: true,
    });
  },
  
  toSkuPage(skuId) {
    console.log(skuId);
    console.log(this.skuId);
    reportClick(`ljgm-${skuId}`);
    openEcGood(skuId);
  },

  closePopup() {
    this.setData({
      memberPop: false,
      recordPop: false,
      rulePop: false,
      addressPop: false,
      successApplyPop: false,
      successReceivePop: false,
      sorryPop: false,
    });
  },
});
