class Store {
  data = {
    shopId: '37702451', //抖店 需根据店铺小程序更改
    activityId: '1942403017190907905' /**活动id 需根据店铺小程序更改*/,
    awemeId: '', //抖音号
    platform: '', // 宿主app
    isVersioningSupport: true /**用户得抖音app当前版本是否过低 */,
    openId: '' /**reportAnalytics用 */,
    doudianOpenId: '' /**抖店openId**/,
    token: '' /**网络请求时使用 */,
    actStatus: 1 /**活动状态1-未开始 2-进行中 3-已结束 */,
    rule: '暂无规则' /**规则 */,
    avatar: 'https://img.alicdn.com/imgextra/i4/155168396/O1CN01xIAVdD2BtQAG7VcCm_!!155168396.png' /**用户头像 */,
    hasGetUserInfo: false /**是否获取用户信息 */,
    nickName: '' /**用户昵称 */,
    levelName: '',
    /*用户等级名称*/
    userLevel: '',
    /*用户等级*/
    pointsAmountCent: '',
    /*用户积分，要除以100*/
    isMember: false, //是否入会
    shareTitle: '',
    shareContent: '',
    decoData: '', // 装修数据-
    clickType: 0, // 展示成功或者失败弹窗时-1领取奖品 0-报名 
    applyBtnStatus: 1, // 报名状态：1-未报名 2-已报名 3-不在报名时段 4-没有符合前置sku的订单 5-订单笔数不符合 6-订单金额不满足"
    prizeInfo: {
      // // 测试数据
      // prizeName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      // stock: 100,
      // prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/296618/30/6876/59147/68665867F97ab4de6/f5e7785e4edacc8f.png',
      // receiveBtnStatus: 1, // 领奖按钮状态 1-可以领取 2-已领取 3-奖品已领完 4-没有符合sku的后置订单 5-没有完成n天的订单 6-订单金额不满足"
      prizeName: '',
      stock: 0,
      prizeImg: '',
      receiveBtnStatus: 1, // 领奖按钮状态 1-可以领取 2-已领取 3-奖品已领完 4-没有符合sku的后置订单 5-没有完成n天的订单 6-订单金额不满足"
    }, // 奖品信息
    addressForm: { // 地址表单
      realName: '', // 姓名
      mobile: '', // 手机号
      region: ["", "", ""], // 地址-省市区
      addressDetail: '', // 详细地址
    },
    skuList: [
      // // 测试数据
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
      // {
      //   skuId: '1915020649755488258',
      //   skuName: '贝比佳电动四轮车-儿童玩具沙滩越野车',
      //   skuImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/305690/11/15352/61742/686737e1F860efa32/11dc5c2aa3fa7dde.png',
      // },
    ], // 曝光商品信息
    pageInfo : {
      current: 1,
      size: 10,
    },
  };
  // 设置活动分享字段
  setShareInfo(data) {
    const { shareTitle, shareContent, shareImage } = data;
    if (shareTitle) this.data.shareTitle = shareTitle;
    if (shareContent) this.data.shareTitle = shareContent;
    if (shareImage) this.data.shareTitle = shareImage;
  }
  // 设置会员信息
  setMemberInfo(data) {
    const { levelName, pointsAmountCent, userLevel } = data;
    if (levelName) this.data.levelName = levelName;
    if (userLevel) this.data.userLevel = userLevel;
    if (pointsAmountCent) this.data.pointsAmountCent = pointsAmountCent / 100;
    this.update();
  }
  //设置token
  setToken(data) {
    this.data.token = data;
    this.update();
  }
  setActStatus(data) {
    this.data.actStatus = data;
    this.update();
  }
  setImgInfo(data) {
    this.data.imgInfo = data;
    this.update();
  }
  setRule(data) {
    this.data.rule = data;
    this.update();
  }
  setOpenId(data) {
    this.data.openId = data;
    this.update();
  }
  setDoudianOpenId(data) {
    this.data.doudianOpenId = data;
    this.update();
  }
  setIsVersioningSupport(data) {
    this.data.isVersioningSupport = data;
    this.update();
  }
  setUserInfo(data) {
    this.data.userInfo = data;
    this.update();
  }
  setPopupIndex(data) {
    this.data.popupIndex = data;
    this.update();
  }

  setHasAccessToken(data) {
    this.data.hasAccessToken = data;
    this.update();
  }
  setClickType(data) {
    this.data.clickType = data;
    this.update();
  }
  setApplyBtnStatus(data) {
    this.data.applyBtnStatus = data;
    this.update();
  }
  setPrizeInfo(data) {
    this.data.prizeInfo = data;
    this.update();
  }
  setAddressForm(data) {
    this.data.addressForm = data;
    this.update();
  }
  setHasGetUserInfo(data) {
    this.data.hasGetUserInfo = data;
    this.update();
  }
  setDecoData(data) {
    this.data.decoData = data;
    this.update();
  }
  setPageInfo(data) {
    this.data.pageInfo = { ...this.data.pageInfo, ...data };
    this.update();
  }
  setSkuList(data) {
    this.data.skuList = data;
    this.update();
  }
}

module.exports = new Store();
